{"name": "drops-operation", "dockerComposeFile": "docker-compose.yml", "service": "dev", "workspaceFolder": "/workspace/drops-operation", "features": {"ghcr.io/devcontainers/features/github-cli": {}, "ghcr.io/nils-geistmann/devcontainers-features/zsh": {}, "ghcr.io/devcontainers-extra/features/npm-packages": {}, "ghcr.io/rocker-org/devcontainer-features/apt-packages": {"packages": "inotify-tools git bash less gnupg ssh"}}, "customizations": {"vscode": {"extensions": ["sleistner.vscode-fileutils", "kahole.magit", "JakeBecker.elixir-ls", "Augment.vscode-augment", "GitHub.vscode-pull-request-github", "GitHub.copilot"], "settings": {"terminal.integrated.shell.linux": "/usr/local/bin/zsh", "editor.formatOnSave": true}, "postCreateCommand": "cd ~/dotfiles && git pull --rebase && ./install.sh"}}}